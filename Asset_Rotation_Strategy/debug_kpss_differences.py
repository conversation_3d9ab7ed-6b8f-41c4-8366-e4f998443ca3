#!/usr/bin/env python
"""
Debug GARCH and Wavelet Differences Between PineScript and Python Implementation

This script identifies the specific differences causing divergence between
the PineScript GARCH and Wavelet implementations and our Python versions.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.kpss_test import KPSSTest


def pinescript_linreg(data: pd.Series, length: int, offset: int = 0) -> pd.Series:
    """
    Exact implementation of TradingView's ta.linreg() function
    Based on the formula: linreg = intercept + slope * (length - 1 - offset)
    """
    linreg_values = pd.Series(index=data.index, dtype=float)
    
    for i in range(length - 1, len(data)):
        # Get window data (most recent 'length' values)
        window_data = data.iloc[i - length + 1:i + 1].values
        
        if len(window_data) == length:
            # Create x values (0, 1, 2, ..., length-1)
            x = np.arange(length, dtype=float)
            y = window_data
            
            # Handle NaN values
            valid_mask = ~np.isnan(y)
            if np.sum(valid_mask) > 1:
                x_valid = x[valid_mask]
                y_valid = y[valid_mask]
                
                # Calculate linear regression using least squares
                n = len(x_valid)
                sum_x = np.sum(x_valid)
                sum_y = np.sum(y_valid)
                sum_xy = np.sum(x_valid * y_valid)
                sum_x2 = np.sum(x_valid * x_valid)
                
                # Calculate slope and intercept
                denominator = n * sum_x2 - sum_x * sum_x
                if denominator != 0:
                    slope = (n * sum_xy - sum_x * sum_y) / denominator
                    intercept = (sum_y - slope * sum_x) / n
                    
                    # Apply TradingView formula: linreg = intercept + slope * (length - 1 - offset)
                    linreg_values.iloc[i] = intercept + slope * (length - 1 - offset)
                else:
                    linreg_values.iloc[i] = np.mean(y_valid)
            else:
                linreg_values.iloc[i] = window_data[-1] if not np.isnan(window_data[-1]) else 0
        else:
            linreg_values.iloc[i] = data.iloc[i] if not np.isnan(data.iloc[i]) else 0
    
    return linreg_values.ffill().fillna(0)


def pinescript_kpss_test(residuals: pd.Series, length: int) -> float:
    """
    Exact implementation of PineScript KPSS test function
    """
    if len(residuals) < length:
        return 0.0
    
    # Get the last 'length' residuals
    test_residuals = residuals.iloc[-length:].values
    
    # Calculate partial sums and partial sum squared
    partial_sums = 0.0
    partial_sum_squared = 0.0
    
    for i in range(length):
        # nz function: treat NaN as 0
        residual_val = test_residuals[i] if not np.isnan(test_residuals[i]) else 0.0
        partial_sums += residual_val
        partial_sum_squared += partial_sums * partial_sums
    
    # Calculate number of lags
    lags = int(np.floor(np.sqrt(length)))
    
    # Calculate sum of squared residuals
    sum_residuals = 0.0
    for i in range(length):
        residual_val = test_residuals[i] if not np.isnan(test_residuals[i]) else 0.0
        sum_residuals += residual_val * residual_val
    
    # Add autocovariance terms
    for lag in range(1, lags + 1):
        weight = 1.0 - lag / (lags + 1.0)
        lag_sum = 0.0
        
        for i in range(lag, length):
            residual_i = test_residuals[i] if not np.isnan(test_residuals[i]) else 0.0
            residual_i_lag = test_residuals[i - lag] if not np.isnan(test_residuals[i - lag]) else 0.0
            lag_sum += residual_i * residual_i_lag
        
        sum_residuals += 2 * weight * lag_sum
    
    # Calculate long-run variance
    long_run_variance = sum_residuals / length
    
    # Prevent division by zero
    if long_run_variance <= 0:
        return 0.0
    
    # Calculate KPSS statistic
    kpss_stat = partial_sum_squared / (length * length * long_run_variance)
    
    return kpss_stat


def compare_implementations(data: pd.DataFrame, length: int = 85) -> Dict[str, Any]:
    """
    Compare PineScript-exact implementation with our current Python implementation
    """
    source = data['close']
    
    print(f"Comparing KPSS implementations with length={length}")
    print(f"Data range: {data.index[0].date()} to {data.index[-1].date()}")
    print(f"Total data points: {len(data)}")
    
    # 1. Compare linear regression implementations
    print("\n" + "="*60)
    print("1. COMPARING LINEAR REGRESSION IMPLEMENTATIONS")
    print("="*60)
    
    # Our current implementation
    kpss_test = KPSSTest(length=length)
    our_linreg = kpss_test._linear_regression(source, length)
    
    # PineScript-exact implementation
    pinescript_linreg_vals = pinescript_linreg(source, length, offset=0)
    
    # Compare linreg values
    linreg_diff = np.abs(our_linreg - pinescript_linreg_vals).dropna()
    max_linreg_diff = linreg_diff.max()
    mean_linreg_diff = linreg_diff.mean()
    
    print(f"Linear regression differences:")
    print(f"  Max difference: {max_linreg_diff:.10f}")
    print(f"  Mean difference: {mean_linreg_diff:.10f}")
    print(f"  Non-zero differences: {(linreg_diff > 1e-10).sum()}/{len(linreg_diff)}")
    
    # Show some specific examples
    if max_linreg_diff > 1e-6:
        print(f"\nLargest differences (first 5):")
        largest_diffs = linreg_diff.nlargest(5)
        for idx, diff in largest_diffs.items():
            print(f"  {idx.date()}: Our={our_linreg[idx]:.8f}, PS={pinescript_linreg_vals[idx]:.8f}, Diff={diff:.8f}")
    
    # 2. Compare residuals
    print("\n" + "="*60)
    print("2. COMPARING RESIDUALS")
    print("="*60)
    
    our_residuals = source - our_linreg
    pinescript_residuals = source - pinescript_linreg_vals
    
    residuals_diff = np.abs(our_residuals - pinescript_residuals).dropna()
    max_residuals_diff = residuals_diff.max()
    mean_residuals_diff = residuals_diff.mean()
    
    print(f"Residuals differences:")
    print(f"  Max difference: {max_residuals_diff:.10f}")
    print(f"  Mean difference: {mean_residuals_diff:.10f}")
    print(f"  Non-zero differences: {(residuals_diff > 1e-10).sum()}/{len(residuals_diff)}")
    
    # 3. Compare KPSS statistics
    print("\n" + "="*60)
    print("3. COMPARING KPSS STATISTICS")
    print("="*60)

    # Calculate KPSS stats for both implementations
    our_kpss_stats = pd.Series(index=source.index, dtype=float)
    pinescript_kpss_stats = pd.Series(index=source.index, dtype=float)

    for i in range(length - 1, len(source)):
        # Our implementation
        window_residuals_our = our_residuals.iloc[i - length + 1:i + 1]
        if len(window_residuals_our) == length:
            our_kpss_stats.iloc[i] = kpss_test._kpss_statistic(window_residuals_our, length)

        # PineScript implementation
        window_residuals_ps = pinescript_residuals.iloc[i - length + 1:i + 1]
        if len(window_residuals_ps) == length:
            pinescript_kpss_stats.iloc[i] = pinescript_kpss_test(window_residuals_ps, length)

    # Compare KPSS statistics
    kpss_diff = np.abs(our_kpss_stats - pinescript_kpss_stats).dropna()
    max_kpss_diff = kpss_diff.max()
    mean_kpss_diff = kpss_diff.mean()

    print(f"KPSS statistics differences:")
    print(f"  Max difference: {max_kpss_diff:.10f}")
    print(f"  Mean difference: {mean_kpss_diff:.10f}")
    print(f"  Non-zero differences: {(kpss_diff > 1e-10).sum()}/{len(kpss_diff)}")

    # Show some specific examples
    if max_kpss_diff > 1e-6:
        print(f"\nLargest KPSS differences (first 5):")
        largest_kpss_diffs = kpss_diff.nlargest(5)
        for idx, diff in largest_kpss_diffs.items():
            print(f"  {idx.date()}: Our={our_kpss_stats[idx]:.8f}, PS={pinescript_kpss_stats[idx]:.8f}, Diff={diff:.8f}")

    # 4. Compare normalized values and rolling min/max
    print("\n" + "="*60)
    print("4. COMPARING NORMALIZED VALUES AND ROLLING MIN/MAX")
    print("="*60)

    # Our normalized implementation
    our_normalized = kpss_test.calculate(data)

    # Get our rolling min/max values
    our_max_kpss = our_kpss_stats.rolling(window=length, min_periods=1).max()
    our_min_kpss = our_kpss_stats.rolling(window=length, min_periods=1).min()

    # PineScript normalized implementation
    max_kpss_ps = pinescript_kpss_stats.rolling(window=length, min_periods=1).max()
    min_kpss_ps = pinescript_kpss_stats.rolling(window=length, min_periods=1).min()

    # Compare rolling min/max values
    max_diff = np.abs(our_max_kpss - max_kpss_ps).dropna()
    min_diff = np.abs(our_min_kpss - min_kpss_ps).dropna()

    print(f"Rolling max differences:")
    print(f"  Max difference: {max_diff.max():.10f}")
    print(f"  Mean difference: {max_diff.mean():.10f}")

    print(f"Rolling min differences:")
    print(f"  Max difference: {min_diff.max():.10f}")
    print(f"  Mean difference: {min_diff.mean():.10f}")

    # Normalize using the same function
    def normalize(value, max_value, min_value):
        return 2 * ((value - min_value) / (max_value - min_value)) - 1

    pinescript_normalized = normalize(pinescript_kpss_stats, max_kpss_ps, min_kpss_ps).fillna(0)

    # Compare normalized values
    normalized_diff = np.abs(our_normalized - pinescript_normalized).dropna()
    max_normalized_diff = normalized_diff.max()
    mean_normalized_diff = normalized_diff.mean()

    print(f"\nNormalized KPSS differences:")
    print(f"  Max difference: {max_normalized_diff:.10f}")
    print(f"  Mean difference: {mean_normalized_diff:.10f}")
    print(f"  Non-zero differences: {(normalized_diff > 1e-6).sum()}/{len(normalized_diff)}")

    # Show some specific examples with detailed breakdown
    if max_normalized_diff > 1e-3:
        print(f"\nDetailed analysis of largest differences (first 3):")
        largest_norm_diffs = normalized_diff.nlargest(3)
        for idx, diff in largest_norm_diffs.items():
            print(f"\n  Date: {idx.date()}")
            print(f"    KPSS stat - Our: {our_kpss_stats[idx]:.8f}, PS: {pinescript_kpss_stats[idx]:.8f}")
            print(f"    Rolling max - Our: {our_max_kpss[idx]:.8f}, PS: {max_kpss_ps[idx]:.8f}")
            print(f"    Rolling min - Our: {our_min_kpss[idx]:.8f}, PS: {min_kpss_ps[idx]:.8f}")
            print(f"    Normalized - Our: {our_normalized[idx]:.6f}, PS: {pinescript_normalized[idx]:.6f}")
            print(f"    Difference: {diff:.6f}")

    return {
        'our_linreg': our_linreg,
        'pinescript_linreg': pinescript_linreg_vals,
        'our_residuals': our_residuals,
        'pinescript_residuals': pinescript_residuals,
        'our_kpss_stats': our_kpss_stats,
        'pinescript_kpss_stats': pinescript_kpss_stats,
        'our_normalized': our_normalized,
        'pinescript_normalized': pinescript_normalized,
        'max_linreg_diff': max_linreg_diff,
        'max_kpss_diff': max_kpss_diff,
        'max_normalized_diff': max_normalized_diff
    }


def plot_comparison(data: pd.DataFrame, comparison_results: Dict[str, Any], length: int = 85):
    """
    Plot comparison between implementations
    """
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))

    # Plot 1: Linear regression comparison
    axes[0].plot(data.index, comparison_results['our_linreg'], label='Our LinReg', alpha=0.7)
    axes[0].plot(data.index, comparison_results['pinescript_linreg'], label='PineScript LinReg', alpha=0.7, linestyle='--')
    axes[0].set_title(f'Linear Regression Comparison (Max Diff: {comparison_results["max_linreg_diff"]:.2e})')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # Plot 2: Residuals comparison
    axes[1].plot(data.index, comparison_results['our_residuals'], label='Our Residuals', alpha=0.7)
    axes[1].plot(data.index, comparison_results['pinescript_residuals'], label='PineScript Residuals', alpha=0.7, linestyle='--')
    axes[1].set_title('Residuals Comparison')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # Plot 3: KPSS statistics comparison
    axes[2].plot(data.index, comparison_results['our_kpss_stats'], label='Our KPSS', alpha=0.7)
    axes[2].plot(data.index, comparison_results['pinescript_kpss_stats'], label='PineScript KPSS', alpha=0.7, linestyle='--')
    axes[2].set_title(f'KPSS Statistics Comparison (Max Diff: {comparison_results["max_kpss_diff"]:.2e})')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)

    # Plot 4: Normalized values comparison
    axes[3].plot(data.index, comparison_results['our_normalized'], label='Our Normalized', alpha=0.7)
    axes[3].plot(data.index, comparison_results['pinescript_normalized'], label='PineScript Normalized', alpha=0.7, linestyle='--')
    axes[3].set_title(f'Normalized KPSS Comparison (Max Diff: {comparison_results["max_normalized_diff"]:.2e})')
    axes[3].legend()
    axes[3].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('kpss_comparison_debug.png', dpi=300, bbox_inches='tight')
    plt.show()


def main():
    """Main debugging function"""
    print("🔍 KPSS TEST DEBUGGING")
    print("=" * 50)

    # Fetch test data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )

    btc_data = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_data)} candles from {btc_data.index[0].date()} to {btc_data.index[-1].date()}")

    # Test with PineScript default length
    length = 85

    # Compare implementations
    comparison_results = compare_implementations(btc_data, length=length)

    # Plot results
    plot_comparison(btc_data, comparison_results, length=length)

    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)

    if comparison_results['max_linreg_diff'] > 1e-6:
        print("❌ LINEAR REGRESSION: Significant differences found")
        print("   This is likely the root cause of KPSS divergence")
    else:
        print("✅ LINEAR REGRESSION: Implementations match closely")

    if comparison_results['max_kpss_diff'] > 1e-6:
        print("❌ KPSS STATISTIC: Significant differences found")
    else:
        print("✅ KPSS STATISTIC: Implementations match closely")

    if comparison_results['max_normalized_diff'] > 1e-3:
        print("❌ NORMALIZED VALUES: Significant differences found")
        print("   This will cause regime detection differences")
    else:
        print("✅ NORMALIZED VALUES: Implementations match closely")

    print(f"\nComparison plot saved as: kpss_comparison_debug.png")


if __name__ == "__main__":
    main()

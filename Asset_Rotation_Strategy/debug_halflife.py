#!/usr/bin/env python
"""
Debug Half-Life Differences Between PineScript and Python Implementation

This script identifies the specific differences causing divergence between
the PineScript Half-Life implementation and our Python version.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.halflife_calculation import HalfLifeCalculation


def pinescript_linreg(data: pd.Series, length: int, offset: int = 0) -> pd.Series:
    """
    Exact implementation of TradingView's ta.linreg() function
    """
    linreg_values = pd.Series(index=data.index, dtype=float)
    
    for i in range(length - 1, len(data)):
        window_data = data.iloc[i - length + 1:i + 1].values
        
        if len(window_data) == length:
            x = np.arange(length, dtype=float)
            y = window_data
            
            valid_mask = ~np.isnan(y)
            if np.sum(valid_mask) > 1:
                x_valid = x[valid_mask]
                y_valid = y[valid_mask]
                
                n = len(x_valid)
                sum_x = np.sum(x_valid)
                sum_y = np.sum(y_valid)
                sum_xy = np.sum(x_valid * y_valid)
                sum_x2 = np.sum(x_valid * x_valid)
                
                denominator = n * sum_x2 - sum_x * sum_x
                if denominator != 0:
                    slope = (n * sum_xy - sum_x * sum_y) / denominator
                    intercept = (sum_y - slope * sum_x) / n
                    
                    # Apply TradingView formula: linreg = intercept + slope * (length - 1 - offset)
                    linreg_values.iloc[i] = intercept + slope * (length - 1 - offset)
                else:
                    linreg_values.iloc[i] = np.mean(y_valid)
            else:
                linreg_values.iloc[i] = window_data[-1] if not np.isnan(window_data[-1]) else 0
        else:
            linreg_values.iloc[i] = data.iloc[i] if not np.isnan(data.iloc[i]) else 0
    
    return linreg_values.fillna(0)


def pinescript_halflife(source: pd.Series, lookback: int = 100) -> pd.Series:
    """
    Exact implementation of PineScript Half-Life calculation
    """
    # PineScript return_percent function: ta.change(src) * 100 / src[1]
    # ta.change(src) is equivalent to src - src[1]
    returns = (source - source.shift(1)) * 100 / source.shift(1)
    returns = returns.fillna(0)
    
    # Mean price: ta.sma(source, lookback_hl)
    mean_price = source.rolling(window=lookback, min_periods=1).mean()
    
    # Linear regression slope: ta.linreg(source, lookback_hl, 0)
    slope = pinescript_linreg(source, lookback, offset=0)
    
    # Standard deviations: ta.stdev(source, lookback_hl) and ta.stdev(source[1], lookback_hl)
    current_std = source.rolling(window=lookback, min_periods=1).std()
    lagged_std = source.shift(1).rolling(window=lookback, min_periods=1).std()
    
    # Theta calculation: slope * (ta.stdev(source, lookback_hl) / ta.stdev(source[1], lookback_hl))
    theta = slope * (current_std / lagged_std.replace(0, 1e-9))  # Prevent division by zero
    
    # Half-life calculation: math.log(2) / (theta + 1e-9)
    half_life = np.log(2) / (theta + 1e-9)
    
    return half_life.fillna(0)


def compare_halflife_implementations(data: pd.DataFrame, lookback: int = 100) -> Dict[str, Any]:
    """
    Compare Half-Life implementations step by step
    """
    source = data['close']
    
    print(f"Comparing Half-Life implementations with lookback={lookback}")
    print(f"Data range: {data.index[0].date()} to {data.index[-1].date()}")
    
    # Our implementation
    halflife_calc = HalfLifeCalculation(lookback=lookback)
    our_halflife = halflife_calc.calculate(data)
    
    # PineScript implementation
    pinescript_halflife_raw = pinescript_halflife(source, lookback)
    
    # PineScript normalization: -normalize(half_life, max_half_life, min_half_life)
    max_halflife_ps = pinescript_halflife_raw.rolling(window=lookback, min_periods=1).max()
    min_halflife_ps = pinescript_halflife_raw.rolling(window=lookback, min_periods=1).min()
    
    def normalize(value, max_value, min_value):
        return 2 * ((value - min_value) / (max_value - min_value)) - 1
    
    # Note the negative sign in PineScript
    pinescript_normalized = -normalize(pinescript_halflife_raw, max_halflife_ps, min_halflife_ps).fillna(0)
    
    # Compare raw half-life values first
    raw_diff = np.abs(pinescript_halflife_raw - halflife_calc._calculate_half_life(
        halflife_calc._calculate_theta(source, lookback))).dropna()
    
    print(f"\nRaw Half-Life differences:")
    print(f"  Max difference: {raw_diff.max():.10f}")
    print(f"  Mean difference: {raw_diff.mean():.10f}")
    
    # Compare normalized values
    normalized_diff = np.abs(our_halflife - pinescript_normalized).dropna()
    max_normalized_diff = normalized_diff.max()
    mean_normalized_diff = normalized_diff.mean()
    
    print(f"\nNormalized Half-Life differences:")
    print(f"  Max difference: {max_normalized_diff:.10f}")
    print(f"  Mean difference: {mean_normalized_diff:.10f}")
    print(f"  Non-zero differences: {(normalized_diff > 1e-6).sum()}/{len(normalized_diff)}")
    
    if max_normalized_diff > 1e-3:
        print(f"\nLargest normalized differences (first 5):")
        largest_diffs = normalized_diff.nlargest(5)
        for idx, diff in largest_diffs.items():
            print(f"  {idx.date()}: Our={our_halflife[idx]:.6f}, PS={pinescript_normalized[idx]:.6f}, Diff={diff:.6f}")
    
    return {
        'our_halflife': our_halflife,
        'pinescript_halflife': pinescript_normalized,
        'raw_pinescript': pinescript_halflife_raw,
        'max_diff': max_normalized_diff
    }


def main():
    """Main debugging function"""
    print("🔍 HALF-LIFE DEBUGGING")
    print("=" * 50)
    
    # Fetch test data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_data = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_data)} candles from {btc_data.index[0].date()} to {btc_data.index[-1].date()}")
    
    # Compare implementations
    halflife_results = compare_halflife_implementations(btc_data, lookback=100)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if halflife_results['max_diff'] > 1e-3:
        print("❌ HALF-LIFE: Significant differences found")
        print("   Issues likely in:")
        print("   - Return calculation method")
        print("   - Linear regression implementation")
        print("   - Standard deviation calculation")
        print("   - Normalization approach")
    else:
        print("✅ HALF-LIFE: Implementations match closely")


if __name__ == "__main__":
    main()

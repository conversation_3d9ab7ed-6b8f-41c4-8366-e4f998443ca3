#!/usr/bin/env python3
"""
Test script to verify fusion signal integration with the main program.

This script tests:
1. Basic functionality without fusion (baseline)
2. Fusion signal integration with different trend methods
3. Parameter validation
4. Error handling
"""

import sys
import os
import logging
from datetime import datetime

# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_basic_functionality():
    """Test basic functionality without fusion signal."""
    print("\n" + "="*60)
    print("TEST 1: BASIC FUNCTIONALITY (NO FUSION)")
    print("="*60)
    
    try:
        from main_program import AllocationTester
        
        # Test with minimal parameters
        tester = AllocationTester(
            timeframe='1d',
            analysis_start_date='2024-01-01',
            analysis_end_date='2024-01-10',
            n_assets=1,
            selected_assets=['BTC/USDT', 'ETH/USDT'],
            use_cache=True,
            use_mtpi=False,
            use_fusion_signal=False  # Disabled
        )
        
        print("✅ AllocationTester created successfully without fusion")
        
        # Test data fetching
        tester.fetch_data()
        print(f"✅ Data fetched: {len(tester.data_dict)} assets")
        
        # Test score calculation
        tester.calculate_scores()
        print(f"✅ Scores calculated: {tester.daily_scores_df.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_fusion_with_rsi():
    """Test fusion signal integration with RSI trend method."""
    print("\n" + "="*60)
    print("TEST 2: FUSION WITH RSI TREND METHOD")
    print("="*60)
    
    try:
        from main_program import AllocationTester
        
        # Test with fusion enabled and RSI trend method
        tester = AllocationTester(
            timeframe='1d',
            analysis_start_date='2024-01-01',
            analysis_end_date='2024-01-10',
            n_assets=1,
            selected_assets=['BTC/USDT', 'ETH/USDT'],
            use_cache=True,
            use_mtpi=False,
            trend_method='RSI',
            use_fusion_signal=True,  # Enabled
            fusion_trend_threshold=0.1,
            fusion_revert_threshold=-0.1,
            fusion_smoothing_length=14
        )
        
        print("✅ AllocationTester created successfully with fusion + RSI")
        
        # Test data fetching
        tester.fetch_data()
        print(f"✅ Data fetched: {len(tester.data_dict)} assets")
        
        # Test score calculation with fusion
        tester.calculate_scores()
        print(f"✅ Scores calculated with fusion: {tester.daily_scores_df.shape}")
        
        # Check if fusion filtering was applied
        non_zero_scores = (tester.daily_scores_df != 0).sum().sum()
        print(f"📊 Non-zero scores after fusion filtering: {non_zero_scores}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fusion + RSI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fusion_with_pgo():
    """Test fusion signal integration with PGO trend method."""
    print("\n" + "="*60)
    print("TEST 3: FUSION WITH PGO TREND METHOD")
    print("="*60)
    
    try:
        from main_program import AllocationTester
        
        # Test with fusion enabled and PGO trend method
        tester = AllocationTester(
            timeframe='1d',
            analysis_start_date='2024-01-01',
            analysis_end_date='2024-01-10',
            n_assets=1,
            selected_assets=['BTC/USDT', 'ETH/USDT'],
            use_cache=True,
            use_mtpi=False,
            trend_method='PGO For Loop',
            use_fusion_signal=True,  # Enabled
            fusion_trend_threshold=0.2,  # Different threshold
            fusion_revert_threshold=-0.2,
            fusion_smoothing_length=10
        )
        
        print("✅ AllocationTester created successfully with fusion + PGO")
        
        # Test data fetching
        tester.fetch_data()
        print(f"✅ Data fetched: {len(tester.data_dict)} assets")
        
        # Test score calculation with fusion
        tester.calculate_scores()
        print(f"✅ Scores calculated with fusion: {tester.daily_scores_df.shape}")
        
        # Check if fusion filtering was applied
        non_zero_scores = (tester.daily_scores_df != 0).sum().sum()
        print(f"📊 Non-zero scores after fusion filtering: {non_zero_scores}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fusion + PGO test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_validation():
    """Test parameter validation for fusion settings."""
    print("\n" + "="*60)
    print("TEST 4: PARAMETER VALIDATION")
    print("="*60)
    
    try:
        from main_program import AllocationTester
        
        # Test with various parameter combinations
        test_cases = [
            {
                'name': 'High trend threshold',
                'params': {'fusion_trend_threshold': 0.5, 'fusion_revert_threshold': -0.1}
            },
            {
                'name': 'Low revert threshold', 
                'params': {'fusion_trend_threshold': 0.1, 'fusion_revert_threshold': -0.5}
            },
            {
                'name': 'Long smoothing',
                'params': {'fusion_smoothing_length': 30}
            },
            {
                'name': 'No hysteresis',
                'params': {'fusion_retain_previous_signal': False}
            }
        ]
        
        for test_case in test_cases:
            print(f"\n  Testing {test_case['name']}...")
            
            params = {
                'timeframe': '1d',
                'analysis_start_date': '2024-01-01',
                'analysis_end_date': '2024-01-05',
                'n_assets': 1,
                'selected_assets': ['BTC/USDT'],
                'use_cache': True,
                'use_mtpi': False,
                'use_fusion_signal': True,
                **test_case['params']
            }
            
            tester = AllocationTester(**params)
            print(f"    ✅ {test_case['name']} parameters accepted")
        
        return True
        
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        return False

def main():
    """Run all fusion integration tests."""
    print("FUSION SIGNAL INTEGRATION TEST SUITE")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run tests
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Fusion + RSI", test_fusion_with_rsi),
        ("Fusion + PGO", test_fusion_with_pgo),
        ("Parameter Validation", test_parameter_validation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Fusion integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

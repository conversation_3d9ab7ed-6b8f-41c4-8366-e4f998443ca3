"""
Half-life of Mean Reversion Calculation for Fusion System

Translates PineScript Half-life of Mean Reversion calculation to Python.
Estimates how long it takes for price deviations to decay by half, indicating mean reversion strength.
"""

import numpy as np
import pandas as pd
from typing import Optional
from .base_indicator import BaseFusionIndicator


class HalfLifeCalculation(BaseFusionIndicator):
    """Half-life of Mean Reversion implementation for regime detection"""
    
    def __init__(self, lookback: int = 100):
        super().__init__("HalfLife")
        self.lookback = lookback
    
    def _calculate_returns(self, prices: pd.Series) -> pd.Series:
        """
        Calculate percentage returns (matching PineScript exactly)
        PineScript: ta.change(src) * 100 / src[1]

        Args:
            prices: Price series

        Returns:
            Percentage returns series
        """
        # PineScript: ta.change(src) * 100 / src[1]
        # ta.change(src) = src - src[1]
        change = prices - prices.shift(1)
        returns = change * 100 / prices.shift(1)
        return returns.fillna(0)
    
    def _calculate_linear_regression_slope(self, data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling linear regression slope
        
        Args:
            data: Input data series
            window: Rolling window size
            
        Returns:
            Rolling slope series
        """
        slopes = pd.Series(index=data.index, dtype=float)
        
        for i in range(window - 1, len(data)):
            # Get window data
            y = data.iloc[i - window + 1:i + 1].values
            x = np.arange(len(y))
            
            if len(y) > 1:
                # Calculate slope using least squares
                x_mean = np.mean(x)
                y_mean = np.mean(y)
                
                numerator = np.sum((x - x_mean) * (y - y_mean))
                denominator = np.sum((x - x_mean) ** 2)
                
                if denominator != 0:
                    slope = numerator / denominator
                    slopes.iloc[i] = slope
                else:
                    slopes.iloc[i] = 0
            else:
                slopes.iloc[i] = 0
        
        return slopes.fillna(0)

    def _pinescript_linreg(self, data: pd.Series, length: int, offset: int = 0) -> pd.Series:
        """
        Exact implementation of TradingView's ta.linreg() function
        """
        linreg_values = pd.Series(index=data.index, dtype=float)

        for i in range(length - 1, len(data)):
            window_data = data.iloc[i - length + 1:i + 1].values

            if len(window_data) == length:
                x = np.arange(length, dtype=float)
                y = window_data

                valid_mask = ~np.isnan(y)
                if np.sum(valid_mask) > 1:
                    x_valid = x[valid_mask]
                    y_valid = y[valid_mask]

                    n = len(x_valid)
                    sum_x = np.sum(x_valid)
                    sum_y = np.sum(y_valid)
                    sum_xy = np.sum(x_valid * y_valid)
                    sum_x2 = np.sum(x_valid * x_valid)

                    denominator = n * sum_x2 - sum_x * sum_x
                    if denominator != 0:
                        slope = (n * sum_xy - sum_x * sum_y) / denominator
                        intercept = (sum_y - slope * sum_x) / n

                        # Apply TradingView formula: linreg = intercept + slope * (length - 1 - offset)
                        linreg_values.iloc[i] = intercept + slope * (length - 1 - offset)
                    else:
                        linreg_values.iloc[i] = np.mean(y_valid)
                else:
                    linreg_values.iloc[i] = window_data[-1] if not np.isnan(window_data[-1]) else 0
            else:
                linreg_values.iloc[i] = data.iloc[i] if not np.isnan(data.iloc[i]) else 0

        return linreg_values.fillna(0)

    def _calculate_theta(self, prices: pd.Series, window: int) -> pd.Series:
        """
        Calculate theta parameter for half-life estimation (matching PineScript exactly)
        PineScript: theta = slope * (ta.stdev(source, lookback_hl) / ta.stdev(source[1], lookback_hl))

        Args:
            prices: Price series
            window: Rolling window size

        Returns:
            Theta parameter series
        """
        # Use PineScript-matching linear regression: ta.linreg(source, lookback_hl, 0)
        slope = self._pinescript_linreg(prices, window, offset=0)

        # Calculate rolling standard deviations (matching PineScript exactly)
        # ta.stdev(source, lookback_hl)
        current_std = prices.rolling(window=window, min_periods=1).std()
        # ta.stdev(source[1], lookback_hl) - standard deviation of lagged prices
        lagged_std = prices.shift(1).rolling(window=window, min_periods=1).std()

        # Calculate theta: slope * (std_current / std_lagged)
        # Use safe division to prevent division by zero
        std_ratio = self.safe_divide(current_std, lagged_std, 1)
        theta = slope * std_ratio

        return theta.fillna(0)
    
    def _calculate_half_life(self, theta: pd.Series) -> pd.Series:
        """
        Calculate half-life from theta parameter
        
        Args:
            theta: Theta parameter series
            
        Returns:
            Half-life series
        """
        # Half-life = ln(2) / (theta + epsilon)
        # Add small epsilon to prevent division by zero
        epsilon = 1e-9
        half_life = np.log(2) / (theta + epsilon)
        
        # Clip extreme values to reasonable bounds
        half_life = half_life.clip(-1000, 1000)
        
        return half_life
    
    def calculate(self, data: pd.DataFrame, source_column: str = 'close', 
                 **kwargs) -> pd.Series:
        """
        Calculate normalized Half-life of Mean Reversion values
        
        Args:
            data: OHLCV data
            source_column: Column to use for calculation (default: 'close')
            **kwargs: Additional parameters
            
        Returns:
            Normalized half-life values between -1 and 1 (negated as in PineScript)
        """
        if not self.validate_data(data):
            return pd.Series(dtype=float)
        
        if source_column not in data.columns:
            source_column = 'close'
        
        source = data[source_column]
        
        # Calculate theta parameter
        theta = self._calculate_theta(source, self.lookback)
        
        # Calculate half-life
        half_life = self._calculate_half_life(theta)

        # Dynamic normalization (only on valid values, matching PineScript ta.highest/ta.lowest)
        # Create a mask for valid half-life values (non-NaN and finite)
        valid_mask = ~half_life.isna() & np.isfinite(half_life)

        # Initialize normalized series
        normalized_half_life = pd.Series(index=source.index, dtype=float)

        # Calculate rolling min/max only on valid values
        for i in range(len(half_life)):
            if valid_mask.iloc[i]:
                # Get the window of valid values for normalization
                # Look back up to 'lookback' periods, but only consider valid values
                start_idx = max(0, i - self.lookback + 1)
                window_mask = valid_mask.iloc[start_idx:i + 1]
                window_values = half_life.iloc[start_idx:i + 1][window_mask]

                if len(window_values) > 0:
                    max_val = window_values.max()
                    min_val = window_values.min()
                    current_val = half_life.iloc[i]

                    # Apply normalization
                    if max_val != min_val:
                        normalized_val = 2 * ((current_val - min_val) / (max_val - min_val)) - 1
                        # Negate the result as in PineScript: -normalize(half_life, max_half_life, min_half_life)
                        normalized_half_life.iloc[i] = -normalized_val
                    else:
                        normalized_half_life.iloc[i] = 0.0
                else:
                    normalized_half_life.iloc[i] = 0.0
            else:
                normalized_half_life.iloc[i] = 0.0

        return normalized_half_life.fillna(0)
    
    def get_mean_reversion_strength(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get mean reversion strength based on half-life
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with mean reversion strength (0-1 scale, higher = stronger mean reversion)
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        # Shorter half-life indicates stronger mean reversion
        # Convert to strength measure (inverse relationship)
        # Use absolute value and invert
        abs_half_life = np.abs(half_life)
        
        # Avoid division by zero
        strength = self.safe_divide(1, abs_half_life + 1, 0)
        
        # Normalize to 0-1 scale
        max_strength = strength.rolling(window=self.lookback, min_periods=1).max()
        normalized_strength = self.safe_divide(strength, max_strength, 0)
        
        return normalized_strength.fillna(0)
    
    def get_reversion_speed(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get speed of mean reversion (inverse of half-life)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Series with reversion speed
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        # Speed is inverse of half-life (faster reversion = higher speed)
        speed = self.safe_divide(1, np.abs(half_life) + 1e-6, 0)
        
        return speed.fillna(0)
    
    def get_regime_classification(self, data: pd.DataFrame, 
                                fast_reversion_threshold: float = 0.7,
                                slow_reversion_threshold: float = 0.3,
                                **kwargs) -> pd.Series:
        """
        Classify regime based on mean reversion speed
        
        Args:
            data: OHLCV data
            fast_reversion_threshold: Threshold for fast mean reversion
            slow_reversion_threshold: Threshold for slow mean reversion
            **kwargs: Additional parameters
            
        Returns:
            Series with regime classification: 1 (trending), -1 (mean reverting), 0 (neutral)
        """
        reversion_strength = self.get_mean_reversion_strength(data, **kwargs)
        
        # Classify regimes based on reversion strength
        regime = pd.Series(0, index=data.index)  # Default to neutral
        
        # Strong mean reversion (high strength)
        regime[reversion_strength > fast_reversion_threshold] = -1
        
        # Weak mean reversion suggests trending behavior
        regime[reversion_strength < slow_reversion_threshold] = 1
        
        return regime
    
    def get_raw_half_life(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        Get raw half-life values (not normalized)
        
        Args:
            data: OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            Raw half-life series
        """
        source = data['close']
        theta = self._calculate_theta(source, self.lookback)
        half_life = self._calculate_half_life(theta)
        
        return half_life.fillna(0)

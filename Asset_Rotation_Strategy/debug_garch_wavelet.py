#!/usr/bin/env python
"""
Debug GARCH and Wavelet Differences Between PineScript and Python Implementation

This script identifies the specific differences causing divergence between
the PineScript GARCH and Wavelet implementations and our Python versions.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.garch_model import GARCHModel
from fusion.wavelet_transform import WaveletTransform


def pinescript_garch(source: pd.Series, length: int = 30, alpha: float = 0.1, beta: float = 0.85) -> pd.Series:
    """
    Exact implementation of PineScript GARCH model
    """
    # Calculate mu (simple moving average)
    mu = source.rolling(window=length, min_periods=1).mean()
    
    # Initialize variance series
    variance = pd.Series(index=source.index, dtype=float)
    
    # Calculate sum_residuals_garch for initial variance
    sum_residuals_garch = 0.0
    for i in range(min(length, len(source))):
        if not pd.isna(mu.iloc[i]):
            sum_residuals_garch += (source.iloc[i] - mu.iloc[i]) ** 2
    
    initial_variance = sum_residuals_garch / min(length, len(source)) if len(source) > 0 else 0.0
    
    # Calculate GARCH variance recursively (matching PineScript exactly)
    for i in range(len(source)):
        if i == 0:
            variance.iloc[i] = initial_variance
        else:
            # Current residual squared
            current_residual_sq = (source.iloc[i] - mu.iloc[i]) ** 2 if not pd.isna(mu.iloc[i]) else 0.0
            
            # Previous variance (nz function in PineScript)
            prev_variance = variance.iloc[i-1] if not pd.isna(variance.iloc[i-1]) else initial_variance
            
            # GARCH equation: variance := alpha * (source - mu)^2 + beta * nz(variance[1], initial_variance)
            variance.iloc[i] = alpha * current_residual_sq + beta * prev_variance
    
    # Calculate standard deviation
    std_dev = np.sqrt(variance.fillna(0))
    
    return std_dev


def pinescript_haar_wavelet(source: pd.Series, length: int) -> pd.Series:
    """
    Exact implementation of PineScript Haar Wavelet
    """
    wavelet_result = pd.Series(index=source.index, dtype=float)

    for i in range(length - 1, len(source)):
        # Get window data (matching PineScript indexing: source[i], source[i-1], ..., source[i-length+1])
        window_data = source.iloc[i - length + 1:i + 1].values

        if len(window_data) == length:
            wavelet_sum = 0.0
            for j in range(length):
                # PineScript: for i = 0 to length - 1, weight = (i % 2 == 0 ? 1 : -1), sum += weight * src[i]
                # where src[i] means i bars back from current
                weight = 1 if j % 2 == 0 else -1
                # In PineScript, src[0] = current, src[1] = 1 bar back, etc.
                # Our window_data[0] is oldest (length-1 bars back), window_data[-1] is newest (current)
                # So src[j] corresponds to window_data[-(j+1)]
                value = window_data[-(j+1)]
                wavelet_sum += weight * value

            wavelet_result.iloc[i] = wavelet_sum / length
        else:
            wavelet_result.iloc[i] = 0.0

    return wavelet_result.fillna(0)


def compare_garch_implementations(data: pd.DataFrame, length: int = 30) -> Dict[str, Any]:
    """
    Compare GARCH implementations
    """
    source = data['close']
    
    print(f"Comparing GARCH implementations with length={length}")
    print(f"Data range: {data.index[0].date()} to {data.index[-1].date()}")
    
    # Our implementation
    garch_model = GARCHModel(length=length, alpha=0.1, beta=0.85)
    our_garch = garch_model.calculate(data)
    
    # PineScript implementation
    pinescript_garch_vals = pinescript_garch(source, length, alpha=0.1, beta=0.85)
    
    # PineScript normalization
    max_volatility_ps = pinescript_garch_vals.rolling(window=length, min_periods=1).max()
    min_volatility_ps = pinescript_garch_vals.rolling(window=length, min_periods=1).min()
    
    def normalize(value, max_value, min_value):
        return 2 * ((value - min_value) / (max_value - min_value)) - 1
    
    pinescript_normalized = normalize(pinescript_garch_vals, max_volatility_ps, min_volatility_ps).fillna(0)
    
    # Compare results
    garch_diff = np.abs(our_garch - pinescript_normalized).dropna()
    max_garch_diff = garch_diff.max()
    mean_garch_diff = garch_diff.mean()
    
    print(f"\nGARCH differences:")
    print(f"  Max difference: {max_garch_diff:.10f}")
    print(f"  Mean difference: {mean_garch_diff:.10f}")
    print(f"  Non-zero differences: {(garch_diff > 1e-6).sum()}/{len(garch_diff)}")
    
    if max_garch_diff > 1e-3:
        print(f"\nLargest GARCH differences (first 5):")
        largest_diffs = garch_diff.nlargest(5)
        for idx, diff in largest_diffs.items():
            print(f"  {idx.date()}: Our={our_garch[idx]:.6f}, PS={pinescript_normalized[idx]:.6f}, Diff={diff:.6f}")
    
    return {
        'our_garch': our_garch,
        'pinescript_garch': pinescript_normalized,
        'max_diff': max_garch_diff
    }


def compare_wavelet_implementations(data: pd.DataFrame, length: int = 50, smoothing: int = 10) -> Dict[str, Any]:
    """
    Compare Wavelet implementations
    """
    source = data['close']
    
    print(f"\nComparing Wavelet implementations with length={length}, smoothing={smoothing}")
    
    # Our implementation
    wavelet_model = WaveletTransform(length=length, smoothing_length=smoothing)
    our_wavelet = wavelet_model.calculate(data)
    
    # PineScript implementation
    pinescript_wavelet_raw = pinescript_haar_wavelet(source, length)
    
    # PineScript smoothing and normalization
    pinescript_smoothed = pinescript_wavelet_raw.rolling(window=smoothing, min_periods=1).mean()
    max_wavelet_ps = pinescript_smoothed.rolling(window=length, min_periods=1).max()
    min_wavelet_ps = pinescript_smoothed.rolling(window=length, min_periods=1).min()
    
    def normalize(value, max_value, min_value):
        return 2 * ((value - min_value) / (max_value - min_value)) - 1
    
    pinescript_normalized = normalize(pinescript_smoothed, max_wavelet_ps, min_wavelet_ps).fillna(0)
    
    # Compare results
    wavelet_diff = np.abs(our_wavelet - pinescript_normalized).dropna()
    max_wavelet_diff = wavelet_diff.max()
    mean_wavelet_diff = wavelet_diff.mean()
    
    print(f"\nWavelet differences:")
    print(f"  Max difference: {max_wavelet_diff:.10f}")
    print(f"  Mean difference: {mean_wavelet_diff:.10f}")
    print(f"  Non-zero differences: {(wavelet_diff > 1e-6).sum()}/{len(wavelet_diff)}")
    
    if max_wavelet_diff > 1e-3:
        print(f"\nLargest Wavelet differences (first 5):")
        largest_diffs = wavelet_diff.nlargest(5)
        for idx, diff in largest_diffs.items():
            print(f"  {idx.date()}: Our={our_wavelet[idx]:.6f}, PS={pinescript_normalized[idx]:.6f}, Diff={diff:.6f}")
    
    return {
        'our_wavelet': our_wavelet,
        'pinescript_wavelet': pinescript_normalized,
        'max_diff': max_wavelet_diff
    }


def main():
    """Main debugging function"""
    print("🔍 GARCH & WAVELET DEBUGGING")
    print("=" * 50)
    
    # Fetch test data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_data = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_data)} candles from {btc_data.index[0].date()} to {btc_data.index[-1].date()}")
    
    # Compare implementations
    garch_results = compare_garch_implementations(btc_data, length=30)
    wavelet_results = compare_wavelet_implementations(btc_data, length=50, smoothing=10)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if garch_results['max_diff'] > 1e-3:
        print("❌ GARCH MODEL: Significant differences found")
    else:
        print("✅ GARCH MODEL: Implementations match closely")
    
    if wavelet_results['max_diff'] > 1e-3:
        print("❌ WAVELET TRANSFORM: Significant differences found")
    else:
        print("✅ WAVELET TRANSFORM: Implementations match closely")


if __name__ == "__main__":
    main()
